import {jest} from '@jest/globals';
import type {OnyxEntry} from 'react-native-onyx';
import * as ReportSecondaryActionUtils from '@libs/ReportSecondaryActionUtils';
import CONST from '@src/CONST';
import type {Policy, Report, Transaction} from '@src/types/onyx';

// Mock the dependencies
jest.mock('@libs/actions/Report', () => ({
    getCurrentUserAccountID: jest.fn(() => 123),
    getCurrentUserEmail: jest.fn(() => '<EMAIL>'),
}));

jest.mock('@libs/PolicyUtils', () => ({
    isPolicyMember: jest.fn(),
}));

jest.mock('@libs/ReportUtils', () => ({
    isCurrentUserSubmitter: jest.fn(),
}));

jest.mock('@libs/TransactionUtils', () => ({
    hasReceiptTransaction: jest.fn(() => false),
    isReceiptBeingScanned: jest.fn(() => false),
    isPending: jest.fn(() => false),
    getTransactionDetails: jest.fn(() => ({amount: 100})),
    getOriginalTransactionWithSplitInfo: jest.fn(() => ({
        isExpenseSplit: false,
        isBillSplit: false,
    })),
}));

describe('ReportSecondaryActionUtils', () => {
    describe('isSplitAction', () => {
        const mockReport: Report = {
            reportID: 'report123',
            policyID: 'policy123',
            managerID: 456,
            total: 100,
        } as Report;

        const mockTransaction: Transaction = {
            transactionID: 'transaction123',
            amount: 100,
        } as Transaction;

        const mockPolicy: Policy = {
            id: 'policy123',
            role: CONST.POLICY.ROLE.ADMIN,
        } as Policy;

        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should return false when user is not a policy member', () => {
            // Mock user as admin but not a policy member
            const {isPolicyMember} = require('@libs/PolicyUtils');
            const {isCurrentUserSubmitter} = require('@libs/ReportUtils');
            
            isPolicyMember.mockReturnValue(false);
            isCurrentUserSubmitter.mockReturnValue(true);

            const result = ReportSecondaryActionUtils.isSplitAction(
                mockReport,
                [mockTransaction],
                mockPolicy
            );

            expect(result).toBe(false);
            expect(isPolicyMember).toHaveBeenCalledWith('<EMAIL>', 'policy123');
        });

        it('should return true when user is policy member and has valid role', () => {
            // Mock user as admin and policy member
            const {isPolicyMember} = require('@libs/PolicyUtils');
            const {isCurrentUserSubmitter} = require('@libs/ReportUtils');
            
            isPolicyMember.mockReturnValue(true);
            isCurrentUserSubmitter.mockReturnValue(true);

            const result = ReportSecondaryActionUtils.isSplitAction(
                mockReport,
                [mockTransaction],
                mockPolicy
            );

            expect(result).toBe(true);
            expect(isPolicyMember).toHaveBeenCalledWith('<EMAIL>', 'policy123');
        });

        it('should return false when user has valid role but is not policy member', () => {
            // Mock user as manager but not policy member
            const {isPolicyMember} = require('@libs/PolicyUtils');
            const {isCurrentUserSubmitter} = require('@libs/ReportUtils');
            const {getCurrentUserAccountID} = require('@libs/actions/Report');
            
            isPolicyMember.mockReturnValue(false);
            isCurrentUserSubmitter.mockReturnValue(false);
            getCurrentUserAccountID.mockReturnValue(456); // Same as managerID

            const result = ReportSecondaryActionUtils.isSplitAction(
                mockReport,
                [mockTransaction],
                mockPolicy
            );

            expect(result).toBe(false);
            expect(isPolicyMember).toHaveBeenCalledWith('<EMAIL>', 'policy123');
        });

        it('should return false when multiple transactions are provided', () => {
            const {isPolicyMember} = require('@libs/PolicyUtils');
            
            isPolicyMember.mockReturnValue(true);

            const result = ReportSecondaryActionUtils.isSplitAction(
                mockReport,
                [mockTransaction, mockTransaction], // Multiple transactions
                mockPolicy
            );

            expect(result).toBe(false);
            // Should not even check policy membership for invalid transaction count
            expect(isPolicyMember).not.toHaveBeenCalled();
        });
    });
});
